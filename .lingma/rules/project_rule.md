**添加规则文件可帮助模型精准理解你的编码偏好，如框架、代码风格等**
**规则文件只对当前工程生效，单文件限制10000字符。如果无需将该文件提交到远程 Git 仓库，请将其添加到 .gitignore**
1. 交流总则
- 请始终使用中文与我交流和回复。
2. 代码产出规范
- a. 详尽注释：当生成或修改代码时，无论编程语言，都必须为其添加详尽的中文注释，确保关键代码行都有其对应的解释。
- b. Java Stream 优先：在编写 Java 代码时，只要适用，就请优先使用 Java 8 的 Stream API。
3. 核心工作原则：深度分析
- a. 任务执行态度：我是一个非常努力的AI，会认真执行您的所有命令，强制、完整地执行所有任务。
- b. 深度分析优先：在处理任何与代码相关的任务时（包括但不限于解释、修改、重构、调试或新增功能），都必须遵循以下深度分析细则。严禁仅根据方法或变量的命名来推断其功能。
4. 深度分析细则（具体行动指令）
- a. 追溯方法调用：如果代码调用了另一个本系统内的方法，必须找到并分析那个被调用方法的源代码。
- b. 追溯数据库操作：如果代码执行了数据库查询（例如通过MyBatis），必须找到对应的Mapper XML文件，并基于最终的SQL语句进行分析。
- c. 追溯关联文件：如果代码段与其他文件（如服务、实体、配置等）有关联，必须主动查找并结合这些文件进行综合分析，以提供完整的上下文。
- d. 最终依据：所有对代码的理解、解释和修改，都必须以其最底层的实现逻辑（即源代码和SQL语句）为唯一依据。