package com.wangchao.springbootdemo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;

@SpringBootApplication
@EnableCaching
@EnableMethodCache(basePackages = "com.wangchao.springbootdemo")
public class SpringbootDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(SpringbootDemoApplication.class, args);
    }

}
