package com.wangchao.springbootdemo.util;

import com.wangchao.springbootdemo.entity.TreeNode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 树形结构构建工具类
 */
public class TreeBuilder {

    /**
     * 将平级的节点列表转换为树形结构
     *
     * @param nodes 平级节点列表
     * @return 树形结构的根节点列表
     */
    public static List<TreeNode> buildTree(List<TreeNode> nodes) {
        // 结果列表
        List<TreeNode> result = new ArrayList<>();
        
        if (nodes == null || nodes.isEmpty()) {
            return result;
        }
        
        // 使用map存储所有节点，key为节点id
        Map<Long, TreeNode> nodeMap = new HashMap<>();
        for (TreeNode node : nodes) {
            nodeMap.put(node.getId(), node);
        }
        
        // 构建树形结构
        for (TreeNode node : nodes) {
            Long parentId = node.getParentId();
            
            // 如果是根节点（parentId为null或0），直接添加到结果列表
            if (parentId == null || parentId == 0L) {
                result.add(node);
            } else {
                // 非根节点，找到其父节点并添加为子节点
                TreeNode parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    parentNode.addChild(node);
                } else {
                    // 如果找不到父节点，作为根节点处理
                    result.add(node);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 根据指定的根节点ID构建树
     *
     * @param nodes 所有节点列表
     * @param rootId 根节点ID
     * @return 构建好的树
     */
    public static TreeNode buildTreeByRootId(List<TreeNode> nodes, Long rootId) {
        if (nodes == null || nodes.isEmpty() || rootId == null) {
            return null;
        }
        
        // 找到根节点
        TreeNode root = null;
        for (TreeNode node : nodes) {
            if (rootId.equals(node.getId())) {
                root = node;
                break;
            }
        }
        
        if (root == null) {
            return null;
        }
        
        // 使用map存储所有节点，key为节点id
        Map<Long, TreeNode> nodeMap = new HashMap<>();
        for (TreeNode node : nodes) {
            nodeMap.put(node.getId(), node);
        }
        
        // 构建树形结构
        for (TreeNode node : nodes) {
            // 跳过根节点
            if (node.getId().equals(rootId)) {
                continue;
            }
            
            Long parentId = node.getParentId();
            if (parentId != null) {
                TreeNode parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    parentNode.addChild(node);
                }
            }
        }
        
        return root;
    }
} 