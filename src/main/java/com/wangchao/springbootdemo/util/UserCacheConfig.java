package com.wangchao.springbootdemo.util;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.wangchao.springbootdemo.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class UserCacheConfig {
    @Autowired
    private CacheManager cacheManager;
    private Cache<Long, User> userCache;


    @PostConstruct
    public void init() {
        userCache = cacheManager.getOrCreateCache(QuickConfig.newBuilder("userCache")
                .cacheType(CacheType.BOTH)
                .localLimit(100)

                .expire(java.time.Duration.ofMinutes(5))
                .loader(key -> {
                    Long userId = (Long) key;
                    User user = new User();
                    user.setId(userId);
                    user.setUsername("User" + userId);
                    return user;
                })
                .build());
    }

    @Bean
    public Cache<Long, User> userCache() {
        return userCache;
    }
}
