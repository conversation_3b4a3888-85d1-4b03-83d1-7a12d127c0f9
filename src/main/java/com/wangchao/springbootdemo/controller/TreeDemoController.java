package com.wangchao.springbootdemo.controller;

import com.wangchao.springbootdemo.entity.TreeNode;
import com.wangchao.springbootdemo.util.TreeBuilder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 树形结构演示控制器
 */
@RestController
@RequestMapping("/api/tree")
public class TreeDemoController {

    /**
     * 获取树形结构示例
     */
    @GetMapping("/demo")
    public List<TreeNode> getTreeDemo() {
        // 创建测试数据
        List<TreeNode> nodes = createTestData();
        
        // 构建树形结构
        return TreeBuilder.buildTree(nodes);
    }
    
    /**
     * 创建测试数据
     */
    private List<TreeNode> createTestData() {
        List<TreeNode> nodes = new ArrayList<>();
        
        // 添加一些测试节点
        nodes.add(new TreeNode(1L, 0L, "根节点1"));
        nodes.add(new TreeNode(2L, 0L, "根节点2"));
        
        nodes.add(new TreeNode(3L, 1L, "子节点1-1"));
        nodes.add(new TreeNode(4L, 1L, "子节点1-2"));
        nodes.add(new TreeNode(5L, 2L, "子节点2-1"));
        
        nodes.add(new TreeNode(6L, 3L, "子节点1-1-1"));
        nodes.add(new TreeNode(7L, 3L, "子节点1-1-2"));
        nodes.add(new TreeNode(8L, 5L, "子节点2-1-1"));
        
        return nodes;
    }
    
    /**
     * 根据指定根节点ID获取树
     */
    @GetMapping("/subtree")
    public TreeNode getSubTree() {
        // 创建测试数据
        List<TreeNode> nodes = createTestData();
        
        // 构建以节点ID为1的子树
        return TreeBuilder.buildTreeByRootId(nodes, 1L);
    }
} 