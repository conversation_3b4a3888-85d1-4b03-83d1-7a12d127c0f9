package com.wangchao.springbootdemo.service;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
public class UserService {

    @Cacheable(value = "userCache", key = "#id")
    public String getUserById(Long id) {
        // 模拟从数据库获取数据（这里用休眠来模拟数据库查询耗时）
        try {
            Thread.sleep(1000); // 模拟耗时操作
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println("从数据库获取用户信息: " + id);
        return "User: " + id;
    }

    @CacheEvict(value = "userCache", key = "#id")
    public void deleteUser(Long id) {
        // 模拟删除操作
        System.out.println("删除用户并清除缓存: " + id);
    }
} 