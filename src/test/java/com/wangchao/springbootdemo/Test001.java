package com.wangchao.springbootdemo;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;

@SpringBootTest
public class Test001 {



    @Test
    public void test(){
        ForkJoinPool pool = new ForkJoinPool(3);
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            System.err.println("hello world");
        },pool);

        CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
            System.err.println("hello world3333");
        },pool);

        CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
            System.err.println("world456");
            return "hello world456";
        });

        try {
            future2.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        CompletableFuture.allOf(future,future1);

        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        String s = future2.join();
        System.err.println(s);

    }

}
